@extends('layout')

@section('content')
<main class="space-y-40 mb-40">
    <section id="intro" class="relative isolate">
        <div class="py-24 sm:py-32 lg:flex lg:items-center lg:gap-x-10 lg:py-40">
            <div class="mx-auto max-w-2xl lg:mx-0 lg:flex-auto">
                <div class="flex">
                    <h2 class="text-[#91FFD7] font-light text-[26px] leading-[90px]">20 - 23 Sep 2025</h2>
                </div>
                <h1 class="font-semibold tracking-tight text-pretty text-gray-900 dark:text-white">Welcome to the biggest Leadership conference in Prague</h1>
                <div class="mt-10 flex items-center gap-x-6">
                    <a href="#" class="primary">Get started</a>
                    <a href="#" class="default"><i class="fa-solid fa-play"></i> Watch Video</a>
                </div>
            </div>
        </div>
    </section>

    <section id="about" class="mt-20">
        <div class="flex flex-col lg:flex-row items-center justify center lg:justify-between gap-16">
            <div class="max-w-[300px]">
                <img src="{{ asset('images/about/about-1.svg') }}" class="w-[60px] text-[#2C956F] mb-12" alt="Project Management" />
                <h5 class="mb-5 text-[26px] font-semibold tracking-tight text-white">Project Management</h5>
                <p class="font-normal text-white">
                    Learn from the best! Describe what attendees can look forward to and why you are experts in this area.
                </p>
            </div>

            <div class="max-w-[300px]">
                <img src="{{ asset('images/about/about-2.svg') }}" class="w-[60px] text-[#2C956F] mb-12" alt="Agile" />
                <h5 class="mb-5 text-[26px] font-semibold tracking-tight text-white">Agile</h5>
                <p class="font-normal text-white">
                    Learn from the best! Describe what attendees can look forward to and why you are experts in this area.
                </p>
            </div>

            <div class="max-w-[300px]">
                <img src="{{ asset('images/about/about-3.svg') }}" class="w-[60px] text-[#2C956F] mb-12" alt="Leadership Experts" />
                <h5 class="mb-5 text-[26px] font-semibold tracking-tight text-white">Leadership Experts</h5>
                <p class="font-normal text-white">
                    Learn from the best! Describe what attendees can look forward to and why you are experts in this area.
                </p>
            </div>
        </div>
    </section>

    <section id="partners">
        <div class="py-24 sm:py-32">
            <div class="mx-auto mt-10 w-full grid grid-cols-2 items-center gap-x-8 gap-y-10 sm:grid-cols-3 sm:gap-x-10 lg:grid-cols-5">
                <s:collection:partners>
                    <img height="60" class="max-h-[60px] w-full object-contain" src="{{ Statamic::tag('glide')->src($logo)->height(160) }}" alt="{{ $title }}" />
                </s:collection:partners>
            </div>
        </div>
    </section>

    <div id="speakers">
        <div class="py-24 sm:py-32">
            <div class="mx-auto max-w-7xl px-6 lg:px-8">
                <div class="mx-auto max-w-2xl lg:mx-0">
                    <h2>{{ $page->speakers_title }}</h2>
                    <p class="mt-6">
                        {{ $page->speakers_desc }}
                    </p>
                </div>
                <ul role="list" class="mx-auto mt-20 grid max-w-2xl grid-cols-{{ $page->speakers_grid_mobile }} gap-x-8 gap-y-16 sm:grid-cols-{{ $page->speakers_grid_tablet }} lg:mx-0 lg:max-w-none lg:grid-cols-{{ $page->speakers_grid_desktop }}">
                    @foreach(\Statamic\Facades\Entry::query()->where('collection', 'speakers')->where('locale', app()->getLocale())->whereStatus('published')->get() as $speaker)
                        <li>
                            <a href="{{ $speaker->url() }}" class="group">
                                <img src="{{ Statamic::tag('glide')->src($speaker->photo)->fit('crop_focal')->width(1024)->height(1024) }}"
                                     class="aspect-3/2 w-full rounded-2xl object-cover transition duration-300 group-hover:scale-105 group-hover:shadow-lg"
                                     alt="{{ $speaker->alt ?? $speaker->title }}"
                                />
                                <h3 class="mt-6 text-lg/8 text-secondary">{{ $speaker->title }}</h3>
                                <p class="text-base/7 text-white">{{ $speaker->position }}</p>
                            </a>
                            <x-socials :speaker="$speaker" />
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>
    </div>

{{-- TODO <x-articles />--}}
</main>
@endsection
